import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link } from "react-router-dom";
// Added PageLayout import
import PageLayout from "@/components/layout/PageLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowRightIcon,
  BoltIcon,
  ShieldCheckIcon,
  ZapIcon,
  StarIcon,
  PhoneIcon,
  DownloadIcon,
  CheckCircleIcon,
  PlusCircleIcon,
  SettingsIcon,
  BatteryChargingIcon,
  GaugeIcon,
  BellIcon,
  CloudIcon,
  ServerIcon,
  FileTextIcon,
  MailIcon
} from "lucide-react";

// Updated UPS series data to match the image with corrected paths
const upsSeries = [
  {
    id: "el-elb",
    name: "EL/ELB Series",
    tagline: "Reliable Power Solutions for Uninterrupted Performance",
    specs: ["1/1 Online UPS", "1 kVA to 3 kVA"],
    description: "Basic power protection with essential features for small office and home equipment.",
    features: ["Zero transfer time", "Pure sine wave output", "Wide input voltage range"],
    image: "/UPS/SB_6-removebg-preview.png",
    path: "/protect/ups/el-series",
    icon: <ZapIcon className="text-blue-400" size={24} />,
    gradient: "bg-gradient-to-r from-blue-500 to-blue-600" // Blue gradient
  },
  {
    id: "eh-11",
    name: "EH-11 Series",
    tagline: "Smaller Footprint with Robust Backup Solutions",
    specs: ["1/1 Online UPS", "6 kVA & 10 kVA"],
    description: "Perfect for small offices and retail environments requiring reliable protection.",
    features: ["Advanced battery management", "LCD status display", "Smart self-diagnostics"],
    image: "/UPS/SB4_-_2-removebg-preview.png",
    path: "/protect/ups/eh-11-series",
    icon: <ZapIcon className="text-blue-400" size={24} />,
    gradient: "bg-gradient-to-r from-blue-600 to-blue-700" // Blue gradient
  },
  {
    id: "eh-31",
    name: "EH-31 Series",
    tagline: "Unmatched Performance for Critical Power Needs",
    specs: ["3/1 Online UPS", "10 kVA & 20 kVA"],
    description: "Ideal for medium-sized businesses with mission-critical applications.",
    features: ["Advanced LCD display", "ECO mode for efficiency", "Smart battery management"],
    image: "/UPS/5-removebg-preview.png",
    path: "/protect/ups/eh-31-series",
    icon: <ZapIcon className="text-blue-400" size={24} />,
    gradient: "bg-gradient-to-r from-blue-500 to-blue-700" // Blue gradient
  },
  {
    id: "eh-33-small",
    name: "EH-33 Series",
    tagline: "High-Frequency Design for Maximum Efficiency",
    specs: ["3/3 Online UPS", "10 kVA to 60 kVA"],
    description: "Designed for data centers and server rooms requiring high reliability.",
    features: ["Parallel operation capability", "Emergency power off", "SNMP communication"],
    image: "/UPS/6-removebg-preview.png",
    path: "/protect/ups/eh-33-small-series",
    icon: <ZapIcon className="text-blue-400" size={24} />,
    gradient: "bg-gradient-to-r from-blue-600 to-blue-800" // Blue gradient
  },
  {
    id: "eh-33-large",
    name: "EH-33 Series",
    tagline: "The High-Frequency Transformer-Less Static Converter UPS",
    specs: ["3/3 Online UPS", "80 kVA to 200 kVA"],
    description: "Advanced UPS solution for large-scale infrastructure requiring high efficiency.",
    features: ["N+X parallel redundancy", "Intelligent battery management", "Low THDi input"],
    image: "/UPS/2__1_-removebg-preview.png",
    path: "/protect/ups/eh-33-large-series",
    icon: <ZapIcon className="text-blue-400" size={24} />,
    gradient: "bg-gradient-to-r from-blue-500 to-blue-700" // Blue gradient
  },
  {
    id: "sx",
    name: "SX Series",
    tagline: "Online Double Conversion with Inbuilt Isolation Transformers",
    specs: ["3/3 On-line UPS", "10 kVA to 120 kVA"],
    description: "Enterprise solution for mission-critical applications requiring zero downtime.",
    features: ["DSP control technology", "Advanced cooling system", "Full LCD touchscreen"],
    image: "/UPS/2-removebg-preview.png",
    path: "/protect/ups/sx-series",
    icon: <BoltIcon className="text-blue-400" size={24} />,
    gradient: "bg-gradient-to-r from-blue-600 to-blue-700" // Blue gradient
  },
  {
    id: "hx",
    name: "HX Series",
    tagline: "Online Double Conversion with Inbuilt Isolation Transformer to Handle Challenging and Regenerative Loads",
    specs: ["3/3 On-line UPS", "40 kVA to 300 kVA"],
    description: "Industrial-grade UPS for large-scale infrastructure and manufacturing.",
    features: ["N+1 parallel redundancy", "Isolation transformer", "Advanced harmonic control"],
    image: "/UPS/1-removebg-preview.png",
    path: "/protect/ups/hx-series",
    icon: <ShieldCheckIcon className="text-blue-400" size={24} />,
    gradient: "bg-gradient-to-r from-blue-600 to-blue-900" // Blue gradient
  }
];

// Enhanced UPS benefits with blue gradient backgrounds for better visibility
const upsBenefits = [
  {
    title: "Zero Downtime",
    description: "Maintain continuous operations with instantaneous switching to battery power when main power fails.",
    icon: <BoltIcon className="text-white" size={36} />,
    gradient: "bg-gradient-to-r from-blue-600 to-blue-800", // Blue gradient
    shadowColor: "shadow-blue-700/30"
  },
  {
    title: "Surge Protection",
    description: "Advanced filtration technology shields sensitive equipment from damaging power surges and spikes.",
    icon: <ShieldCheckIcon className="text-white" size={36} />,
    gradient: "bg-gradient-to-r from-blue-500 to-blue-800", // Blue gradient
    shadowColor: "shadow-blue-700/30"
  },
  {
    title: "Power Conditioning",
    description: "Ensures clean, stable power with voltage regulation, frequency stabilization, and harmonic filtration.",
    icon: <ZapIcon className="text-white" size={36} />,
    gradient: "bg-gradient-to-r from-blue-600 to-blue-800", // Blue gradient
    shadowColor: "shadow-blue-700/30"
  },
  {
    title: "Smart Monitoring",
    description: "Real-time monitoring and alerts enable proactive management of your power infrastructure.",
    icon: <GaugeIcon className="text-white" size={36} />,
    gradient: "bg-gradient-to-r from-blue-500 to-blue-800", // Blue gradient
    shadowColor: "shadow-blue-700/30"
  }
];

// Advanced features showcased with blue gradient backgrounds
const advancedFeatures = [
  {
    title: "Advanced Battery Management",
    description: "Intelligent charging technology extends battery life by up to 30% while providing real-time health monitoring.",
    icon: <BatteryChargingIcon className="text-blue-600" size={32} />,
    gradient: "bg-white dark:bg-gray-800",
    borderGradient: "from-blue-500 to-blue-700"
  },
  {
    title: "Cloud Monitoring",
    description: "Remote monitoring capabilities allow you to manage your UPS systems from anywhere, with instant alerts.",
    icon: <CloudIcon className="text-blue-600" size={32} />,
    gradient: "bg-white dark:bg-gray-800",
    borderGradient: "from-blue-500 to-blue-700"
  },
  {
    title: "Scalable Architecture",
    description: "Parallel operation capability allows your UPS system to grow with your business needs up to 8 units.",
    icon: <ServerIcon className="text-blue-600" size={32} />,
    gradient: "bg-white dark:bg-gray-800",
    borderGradient: "from-blue-500 to-blue-700"
  },
  {
    title: "Predictive Analytics",
    description: "AI-powered analytics predict potential failures before they occur, enabling preventative maintenance.",
    icon: <SettingsIcon className="text-blue-600" size={32} />,
    gradient: "bg-white dark:bg-gray-800",
    borderGradient: "from-blue-500 to-blue-700"
  }
];

// Stacked product card with full image visibility - improved responsive design
const NeomorphicProductCard = ({ series, index }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.5, ease: "easeOut" }}
      className="relative w-full"
    >
      {/* Stacked card container - increased shadow for more depth */}
      <div className="rounded-lg overflow-hidden border border-blue-100 dark:border-blue-800 shadow-2xl hover:shadow-2xl transition-shadow duration-300 hover:border-blue-300 dark:hover:border-blue-600">
        {/* Stacked layout with image side-by-side with content on larger screens */}
        <div className="flex flex-col md:flex-row">
          {/* Image section with increased size and padding - responsive adjustments */}
          <div className="relative md:w-1/2 bg-blue-50/50 dark:bg-blue-900/20 p-4 sm:p-6 md:p-10 flex items-center justify-center">
            {/* Series badge */}
            <div className="absolute top-2 right-2 sm:top-4 sm:right-4 z-10 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100 text-xs px-2 sm:px-3 py-1 rounded-full font-medium">
              {series.specs[0]}
            </div>

            {/* Image with increased max height and enhanced hover effect */}
            <img
              src={series.image}
              alt={series.name}
              loading="lazy"
              className="w-auto h-auto max-h-48 sm:max-h-64 md:max-h-96 object-contain transition-transform duration-500 hover:scale-110"
            />
          </div>

          {/* Content section stacked to the right - adjusted width to match the new image size */}
          <div className="relative md:w-1/2 bg-white dark:bg-gray-800">
            {/* Title section */}
            <div className="p-4 sm:p-6 border-b border-blue-100 dark:border-blue-800/30 bg-gradient-to-r from-blue-50/50 to-white dark:from-blue-900/10 dark:to-gray-800">
              <h3 className="text-xl sm:text-2xl font-bold text-blue-800 dark:text-blue-300 mb-2">{series.name}</h3>
              <p className="text-xs sm:text-sm font-medium text-blue-700 dark:text-blue-400">{series.tagline}</p>
            </div>

            {/* Content section */}
            <div className="p-4 sm:p-6 bg-white dark:bg-gray-800 flex-grow flex flex-col justify-between relative">
              {/* Specs badges */}
              <div className="flex flex-wrap gap-2 mb-3 sm:mb-5">
                {series.specs.slice(1).map((spec, i) => (
                  <span
                    key={i}
                    className="text-xs font-medium px-2 sm:px-3 py-1 rounded-full bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300"
                  >
                    {spec}
                  </span>
                ))}
              </div>

              {/* Description */}
              <p className="text-gray-800 dark:text-gray-200 mb-3 sm:mb-5 text-sm sm:text-base font-medium">
                {series.description}
              </p>

              {/* Features */}
              <div className="space-y-2 sm:space-y-3 mb-4 sm:mb-6">
                {series.features.map((feature, i) => (
                  <div
                    key={i}
                    className="flex items-center gap-2 sm:gap-3 text-gray-800 dark:text-gray-200 text-xs sm:text-sm"
                  >
                    <div className="text-blue-600 dark:text-blue-400">
                      <CheckCircleIcon size={16} />
                    </div>
                    <span className="font-medium">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Action button */}
              <div className="mt-auto">
                <Link to={series.path}>
                  <Button
                    className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white py-2 rounded-lg transition-all duration-300 flex items-center justify-center gap-2"
                  >
                    <span>View Details</span>
                    <ArrowRightIcon className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Modern feature card with border animation - improved responsiveness
const FeatureCard = ({ feature, index }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ delay: index * 0.1, duration: 0.5 }}
      whileHover={{ scale: 1.03 }}
      className="relative group"
    >
      <div className={`relative p-4 sm:p-6 rounded-xl shadow-lg h-full flex flex-col ${feature.gradient}`}>
        <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-clip-padding p-[2px]">
          <div className={`absolute inset-0 rounded-lg bg-gradient-to-r ${feature.borderGradient} blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>
        </div>
        <div className="mb-4 sm:mb-6 p-2 sm:p-3 rounded-xl bg-white/90 dark:bg-gray-800/90 shadow-lg backdrop-blur-sm self-start">
          {feature.icon}
        </div>
        <h3 className="text-base sm:text-lg font-bold mb-2 sm:mb-3 text-gray-800 dark:text-white">{feature.title}</h3>
        <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm">
          {feature.description}
        </p>
      </div>
    </motion.div>
  );
};

// Section with animated counter - improved responsiveness
const StatsItem = ({ value, label, icon, gradient }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let start = 0;
    const end = parseInt(value.substring(0, 3));

    if (start === end) return;

    const timer = setInterval(() => {
      start += 1;
      setCount(start);
      if (start === end) clearInterval(timer);
    }, 20);

    return () => clearInterval(timer);
  }, [value]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      className="relative"
    >
      <div className={`relative rounded-xl shadow-xl p-3 sm:p-4 md:p-6 overflow-hidden ${gradient}`}>
        <div className="flex items-center gap-2 sm:gap-3">
          <div className="p-2 sm:p-3 rounded-lg bg-white/20 backdrop-blur">
            {icon}
          </div>
          <div>
            <div className="text-xl sm:text-2xl md:text-3xl font-bold text-white">
              {count}{value.substring(3)}
            </div>
            <p className="text-white text-opacity-90 font-medium text-xs sm:text-sm">{label}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Simple tab button component with blue color scheme - improved for small screens
const GlowingTabButton = ({ value, label, isActive, onClick }) => {
  return (
    <button
      className={`px-3 sm:px-4 md:px-6 py-2 sm:py-3 rounded-lg transition-all duration-300 text-sm sm:text-base
                ${isActive
                  ? 'bg-blue-600 text-white dark:bg-blue-700'
                  : 'bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-800/40'}`}
      onClick={() => onClick(value)}
    >
      <span className="font-medium">{label}</span>
    </button>
  );
};

// For some components that weren't imported at the top
const BuildingIcon = ({ size, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <rect width="16" height="20" x="4" y="2" rx="2" />
      <path d="M9 22v-4h6v4" />
      <path d="M8 6h.01" />
      <path d="M16 6h.01" />
      <path d="M8 10h.01" />
      <path d="M16 10h.01" />
      <path d="M8 14h.01" />
      <path d="M16 14h.01" />
    </svg>
    );
  };

  const GlobeIcon = ({ size, className }) => {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}
      >
        <circle cx="12" cy="12" r="10" />
        <line x1="2" x2="22" y1="12" y2="12" />
        <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
      </svg>
    );
  };

  // Main UPS component - wrapped with PageLayout with added title
  const UPS = () => {
    const [activeTab, setActiveTab] = useState("overview");

    // Function to open brochure PDF
    const openBrochure = () => {
      // URL to your PDF file - using the specific UPS brochure
      const pdfUrl = "/Krykard Online UPS January 2025. (1).pdf";

      // Open PDF directly in a new tab
      window.open(pdfUrl, '_blank');
    };

    return (
      // PageLayout with title and subtitle for better visibility
      <PageLayout
        title="ONLINE UPS SYSTEM"
        subtitle="Enterprise-grade power protection systems for mission-critical applications"
        category="protect"
      >
        {/* Enhanced styling for animation and visual effects */}
        <style>
          {`
            @keyframes float-slow {
              0% { transform: translateY(0px) rotate(0deg); }
              50% { transform: translateY(-10px) rotate(2deg); }
              100% { transform: translateY(0px) rotate(0deg); }
            }

            @keyframes float-slow-reverse {
              0% { transform: translateY(0px) rotate(0deg); }
              50% { transform: translateY(-10px) rotate(-2deg); }
              100% { transform: translateY(0px) rotate(0deg); }
            }

            @keyframes pulse-glow {
              0% { opacity: 0.3; filter: blur(8px); }
              50% { opacity: 0.8; filter: blur(12px); }
              100% { opacity: 0.3; filter: blur(8px); }
            }

            @keyframes pulse-glow-reverse {
              0% { opacity: 0.6; filter: blur(10px); }
              50% { opacity: 0.2; filter: blur(15px); }
              100% { opacity: 0.6; filter: blur(10px); }
            }

            @keyframes shimmer {
              0% { background-position: -100% 0; }
              100% { background-position: 200% 0; }
            }

            @keyframes gradient-shift {
              0% { background-position: 0% 50%; }
              50% { background-position: 100% 50%; }
              100% { background-position: 0% 50%; }
            }

            @keyframes text-clip {
              from { clip-path: polygon(0 0, 0 0, 0 100%, 0 100%); }
              to { clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%); }
            }

            @keyframes text-fade-in {
              from { opacity: 0; filter: blur(4px); }
              to { opacity: 1; filter: blur(0); }
            }

            @keyframes text-fade-in-up {
              from { opacity: 0; transform: translateY(20px); filter: blur(4px); }
              to { opacity: 1; transform: translateY(0); filter: blur(0); }
            }

            @keyframes rotate-slow {
              from { transform: rotate(0deg); }
              to { transform: rotate(360deg); }
            }

            .animate-float-slow {
              animation: float-slow 6s ease-in-out infinite;
            }

            .animate-float-slow-reverse {
              animation: float-slow-reverse 7s ease-in-out infinite;
            }

            .animate-pulse-glow {
              animation: pulse-glow 8s ease-in-out infinite;
            }

            .animate-pulse-glow-reverse {
              animation: pulse-glow-reverse 9s ease-in-out infinite;
            }

            .animate-shimmer {
              animation: shimmer 8s linear infinite;
              background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.15), transparent);
              background-size: 200% 100%;
            }

            .animate-gradient-shift {
              animation: gradient-shift 15s ease infinite;
              background-size: 200% 200%;
            }

            .animate-text-clip {
              animation: text-clip 1s ease forwards 0.2s;
            }

            .animate-text-fade-in {
              animation: text-fade-in 1s ease forwards;
            }

            .animate-text-fade-in-up {
              animation: text-fade-in-up 1s ease forwards;
            }

            .animate-rotate-slow {
              animation: rotate-slow 30s linear infinite;
            }

            .perspective-1000 {
              perspective: 1000px;
            }

            .glass-effect {
              background: rgba(0, 123, 255, 0.05);
              backdrop-filter: blur(10px);
              -webkit-backdrop-filter: blur(10px);
              border: 1px solid rgba(0, 123, 255, 0.1);
            }

            .text-shadow-blue {
              text-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
            }

            .shadow-blue {
              box-shadow: 0 0 30px rgba(0, 123, 255, 0.3);
            }

            .watermark {
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Ctext x='50%' y='50%' dominant-baseline='middle' text-anchor='middle' font-family='Arial' font-weight='bold' font-size='24' fill='rgba(0, 123, 255, 0.08)' transform='rotate(-45, 100, 100)'%3EKRYKARD%3C/text%3E%3C/svg%3E");
              background-repeat: repeat;
              background-size: 200px 200px;
              pointer-events: none;
              z-index: -1;
            }
          `}
        </style>

        {/* Global watermark */}
        <div className="watermark"></div>        {/* Modern Hero Section with Blue Background Design - following clampmeters pattern */}
        <div className="relative py-6 md:py-12 mb-16 sm:mb-24 md:mb-32 overflow-hidden font-['Open_Sans']">
          {/* Hero Background Elements - Mobile optimized with blue theme */}
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-blue-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
            <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-blue-400 rounded-full opacity-10"></div>
            <div className="absolute top-20 right-20 w-48 h-48 md:w-72 md:h-72 bg-blue-300/20 rounded-full blur-3xl animate-pulse-glow"></div>
            <div className="absolute bottom-10 left-10 w-48 h-48 md:w-72 md:h-72 bg-blue-400/20 rounded-full blur-3xl animate-pulse-glow-reverse"></div>
          </div>

          {/* Decorative elements with blue theme */}
          <div className="absolute -z-10 top-40 right-40 w-96 h-96 rounded-full border border-blue-300/30 animate-rotate-slow"></div>
          <div className="absolute -z-10 bottom-40 left-40 w-80 h-80 rounded-full border border-blue-300/40 animate-rotate-slow" style={{ animationDirection: 'reverse', animationDuration: '25s' }}></div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="container mx-auto px-4"
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-20 items-center">
              {/* Content Section - Now on the left with blue theme */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative z-10 order-2 lg:order-1 space-y-4 text-center lg:text-left"
              >
                {/* KRYKARD Precision Instruments Badge */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="inline-block bg-blue-400 py-1 px-3 rounded-full mb-2"
                >
                  <span className="text-sm md:text-base font-semibold text-white font-['Open_Sans']">KRYKARD Precision Instruments</span>
                </motion.div>

                {/* Enhanced product information with larger text - more responsive now */}
                <div className="mb-6 sm:mb-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans'] mb-2 tracking-tight overflow-hidden">
                      ONLINE <span className="text-blue-400">UPS</span>
                    </h1>
                  </motion.div>

                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: "150px" }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="h-1.5 sm:h-2 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mb-4 sm:mb-6 mx-auto lg:mx-0"
                  />

                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.7 }}
                    className="text-base sm:text-xl md:text-2xl font-medium text-gray-700 dark:text-gray-300 animate-text-fade-in font-['Open_Sans']"
                  >
                    ENTERPRISE-GRADE POWER PROTECTION FOR MISSION-CRITICAL APPLICATIONS
                  </motion.p>
                </div>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                  className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-900 dark:text-gray-200 mb-6 sm:mb-10 leading-relaxed font-medium font-['Open_Sans']"
                >
                  KRYKARD UPS systems provide critical power protection with instantaneous battery backup when main power fails, ensuring continuous operation of essential equipment and preventing costly data loss. Our comprehensive range offers solutions from basic workstation protection to complete enterprise data centers.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 1 }}
                  className="flex flex-wrap gap-3 sm:gap-4"
                >
                  {/* Enhanced Enquiry Button with blue theme */}
                  <Link to="/contact/Sales">
                    <Button
                      className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 rounded-xl shadow-xl hover:shadow-blue-500/30 transition-all duration-300 flex items-center gap-2 text-sm sm:text-base md:text-lg font-semibold"
                    >
                      <MailIcon className="h-4 w-4 sm:h-5 sm:w-5" />
                      <span>Request Demo</span>
                    </Button>
                  </Link>

                  {/* Enhanced Brochure Button with blue theme */}
                  <Button
                    variant="outline"
                    className="border-2 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-800 hover:border-blue-600 px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 rounded-xl transition-all duration-300 flex items-center gap-2 text-sm sm:text-base md:text-lg shadow-lg font-semibold"
                    onClick={openBrochure}
                  >
                    <FileTextIcon className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span>View Brochure</span>
                  </Button>
                </motion.div>
              </motion.div>

              {/* Image Section - Now on the right with blue background theme */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative order-1 lg:order-2 mt-6 sm:mt-0"
              >
                {/* Blue gradient background similar to clampmeters */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-200 to-blue-50 rounded-full opacity-20 blur-xl transform scale-90"></div>

                <motion.div
                  animate={{
                    y: [0, -10, 0],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 3,
                    ease: "easeInOut"
                  }}
                  className="relative z-10 flex justify-center"
                >
                  <div className="relative">
                    <img
                      src="/background_images/ups.png"
                      alt="KRYKARD UPS System"
                      className="max-h-[300px] sm:max-h-[400px] md:max-h-[500px] lg:max-h-[600px] w-auto object-contain drop-shadow-2xl"
                    />
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </div>        {/* Modern Product Showcase Section with 3D effects - improved responsiveness */}
        <div className="relative py-16 sm:py-20 md:py-28 overflow-hidden">
          {/* Simple white background */}
          <div className="absolute inset-0 bg-white dark:bg-gray-900"></div>

          <div className="container mx-auto px-4 relative z-10">{/* Section header with animated elements - improved for mobile */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-10 sm:mb-16 md:mb-20 px-4"
            >
              {/* Animated badge */}
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="inline-block mb-4 sm:mb-6"
              >
                <span className="px-3 sm:px-5 py-1.5 sm:py-2 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 font-medium text-xs sm:text-sm rounded-full shadow-sm">
                  Complete Power Protection Solutions
                </span>
              </motion.div>

              {/* Main heading with animated underline */}
              <motion.h2
                initial={{ y: 20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-gray-900 dark:text-white"
              >
                Our <span className="text-blue-600 dark:text-blue-400 relative">
                  UPS Product Range
                  <motion.div
                    className="absolute -bottom-1 sm:-bottom-2 left-0 h-1 sm:h-1.5 bg-blue-500/70 rounded-full"
                    initial={{ width: 0 }}
                    whileInView={{ width: "100%" }}
                    viewport={{ once: true }}
                    transition={{ duration: 1, delay: 0.6 }}
                  />
                </span>
              </motion.h2>

              {/* Subtitle with staggered animation */}
              <motion.p
                initial={{ y: 20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-base sm:text-lg md:text-xl font-medium max-w-3xl mx-auto text-gray-700 dark:text-gray-300 leading-relaxed"
              >
                From small office setups to enterprise data centers, we offer a comprehensive range of UPS solutions designed for reliability and performance
              </motion.p>
            </motion.div>{/* Simple tab navigation with blue color scheme - improved for mobile */}
            <div className="flex justify-center mb-16 px-2">
              <div className="bg-white dark:bg-gray-800 p-2 sm:p-2.5 rounded-lg overflow-hidden shadow-md border border-blue-100 dark:border-blue-800 w-full max-w-lg sm:max-w-xl md:max-w-3xl">
                <div className="flex flex-wrap justify-center gap-2">
                  {[
                    { value: "overview", label: "All Products" },
                    { value: "el-elb", label: "EL/ELB Series" },
                    { value: "eh", label: "EH Series" },
                    { value: "sx", label: "SX Series" },
                    { value: "hx", label: "HX Series" }
                  ].map((tab) => (
                    <GlowingTabButton
                      key={tab.value}
                      value={tab.value}
                      label={tab.label}
                      isActive={activeTab === tab.value}
                      onClick={setActiveTab}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Enhanced tab content with staggered animations */}
            <AnimatePresence mode="wait">              {activeTab === "overview" && (
                <motion.div
                  key="overview"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}                >
                  <div className="flex flex-col space-y-8 sm:space-y-10 md:space-y-12 max-w-5xl mx-auto">
                    {upsSeries.map((series, index) => (
                      <NeomorphicProductCard key={series.id} series={series} index={index} />
                    ))}
                  </div>
                </motion.div>
              )}

              {activeTab === "el-elb" && (
                <motion.div
                  key="el-elb"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex flex-col space-y-8 sm:space-y-10 md:space-y-12 max-w-5xl mx-auto">
                    {upsSeries
                      .filter(series => series.id === "el-elb")
                      .map((series, index) => (
                        <NeomorphicProductCard key={series.id} series={series} index={index} />
                      ))}
                  </div>
                </motion.div>
              )}

              {activeTab === "eh" && (
                <motion.div
                  key="eh"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}                >                  <div className="flex flex-col space-y-12 max-w-5xl mx-auto">
                    {upsSeries
                      .filter(series => series.id.startsWith("eh"))
                      .map((series, index) => (
                        <NeomorphicProductCard key={series.id} series={series} index={index} />
                      ))}
                  </div>
                </motion.div>
              )}

              {activeTab === "sx" && (
                <motion.div
                  key="sx"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >                  <div className="flex flex-col space-y-12 max-w-5xl mx-auto">
                    {upsSeries
                      .filter(series => series.id === "sx")
                      .map((series, index) => (
                        <NeomorphicProductCard key={series.id} series={series} index={index} />
                      ))}
                  </div>
                </motion.div>
              )}

              {activeTab === "hx" && (
                <motion.div
                  key="hx"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}                >                  <div className="flex flex-col space-y-12 max-w-5xl mx-auto">
                    {upsSeries
                      .filter(series => series.id === "hx")
                      .map((series, index) => (
                        <NeomorphicProductCard key={series.id} series={series} index={index} />
                      ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>        {/* Advanced UPS Technology section - better mobile responsiveness */}
        <div className="relative py-16 sm:py-20 md:py-28 overflow-hidden">
          {/* Enhanced background with subtle patterns and gradients */}
          <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-blue-900/20"></div>

          {/* Animated background elements */}
          <div className="absolute top-40 right-20 -z-10 w-60 sm:w-80 md:w-96 h-60 sm:h-80 md:h-96 rounded-full bg-blue-200/20 dark:bg-blue-500/10 blur-3xl animate-pulse-glow"></div>
          <div className="absolute bottom-40 left-20 -z-10 w-60 sm:w-80 h-60 sm:h-80 rounded-full bg-blue-300/20 dark:bg-blue-600/10 blur-3xl animate-pulse-glow-reverse"></div>

          {/* Decorative grid pattern */}
          <div className="absolute inset-0 -z-10 opacity-[0.03] dark:opacity-[0.05]"
               style={{
                 backgroundImage: "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath d='M0 0h100v100H0z' fill='none'/%3E%3Cpath d='M0 0h1v1H0z' fill='%230066FF'/%3E%3C/svg%3E\")",
                 backgroundSize: "30px 30px"
               }}>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            {/* Section header with enhanced animations */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-20"
            >
              {/* Animated badge with glow effect */}
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="inline-block mb-6"
              >
                <span className="px-5 py-2 bg-gradient-to-r from-blue-500/10 to-blue-600/20 dark:from-blue-500/20 dark:to-blue-400/30 text-blue-700 dark:text-blue-300 font-medium text-sm rounded-full shadow-lg shadow-blue-500/10 dark:shadow-blue-500/5 border border-blue-200/50 dark:border-blue-500/30">
                  Industry-Leading Innovation
                </span>
              </motion.div>

              {/* Main heading with animated gradient text */}
              <motion.h2
                initial={{ y: 20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-5xl font-bold mb-6"
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-500 dark:from-blue-400 dark:to-blue-300">
                  Advanced UPS Technology
                </span>
              </motion.h2>

              {/* Animated underline */}
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                whileInView={{ width: "180px", opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="h-1.5 bg-gradient-to-r from-blue-600 to-blue-400 rounded-full mx-auto mb-8"
              />

              {/* Enhanced description with better typography */}
              <motion.p
                initial={{ y: 20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-xl font-medium max-w-3xl mx-auto text-gray-700 dark:text-gray-300 leading-relaxed"
              >
                Our UPS systems incorporate cutting-edge technology to provide superior power protection
                and intelligent management capabilities for mission-critical applications
              </motion.p>
            </motion.div>

            {/* 3D Feature Cards with enhanced interactive effects */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 perspective-1000">
              {advancedFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30, rotateX: 10 }}
                  whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                  viewport={{ once: true }}
                  transition={{
                    delay: index * 0.15,
                    duration: 0.7,
                    type: "spring",
                    stiffness: 100
                  }}
                  whileHover={{
                    scale: 1.05,
                    rotateY: 5,
                    z: 20,
                    transition: { duration: 0.4 }
                  }}
                  className="relative group"
                >
                  {/* Card with glass morphism effect */}
                  <div className="relative p-8 rounded-2xl h-full flex flex-col bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-blue-100 dark:border-blue-800/50 shadow-xl shadow-blue-500/5 dark:shadow-blue-500/5 group-hover:shadow-blue-500/20 dark:group-hover:shadow-blue-500/10 transition-all duration-500">
                    {/* Glowing background on hover */}
                    <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Animated icon container */}
                    <div className="mb-7 relative">
                      {/* Icon background with animated gradient */}
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 rounded-xl blur-sm transform scale-110 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                      {/* Icon container with floating animation on hover */}
                      <div className="relative p-4 rounded-xl bg-gradient-to-br from-white to-blue-50 dark:from-gray-800 dark:to-gray-900 shadow-lg border border-blue-100 dark:border-blue-800/50 group-hover:border-blue-300 dark:group-hover:border-blue-700 transition-all duration-500 inline-flex">
                        <motion.div
                          animate={{
                            y: [0, -5, 0],
                          }}
                          transition={{
                            duration: 2,
                            ease: "easeInOut",
                            repeat: Infinity,
                            repeatType: "reverse"
                          }}
                          className="text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-all duration-300"
                        >
                          {feature.icon}
                        </motion.div>
                      </div>
                    </div>

                    {/* Title with animated color change */}
                    <h3 className="text-xl font-bold mb-4 text-gray-800 dark:text-white group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-all duration-300">
                      {feature.title}
                    </h3>

                    {/* Description with enhanced readability */}
                    <p className="text-gray-600 dark:text-gray-300 font-medium group-hover:text-gray-800 dark:group-hover:text-white transition-all duration-300">
                      {feature.description}
                    </p>

                    {/* Interactive button that appears on hover */}
                    <div className="mt-6 overflow-hidden h-0 group-hover:h-10 transition-all duration-500 ease-in-out">
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                      >
                        <button className="flex items-center justify-center gap-2 w-full py-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium transition-all duration-300 shadow-md shadow-blue-500/20">
                          <span>Explore</span>
                          <ArrowRightIcon className="h-4 w-4 transform group-hover:translate-x-1 transition-transform" />
                        </button>
                      </motion.div>
                    </div>

                    {/* Decorative corner accent */}
                    <div className="absolute top-0 right-0 w-20 h-20 overflow-hidden">
                      <div className="absolute top-0 right-0 w-5 h-5 rounded-full bg-blue-500/20 dark:bg-blue-400/20 transform translate-x-1/2 -translate-y-1/2 group-hover:scale-[3] transition-all duration-500"></div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>


          </div>
        </div>

        {/* Key UPS Benefits section - moved after products */}
        <div className="relative py-16 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <motion.h2
                className="text-3xl font-bold mb-6 text-gray-900 dark:text-white"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                Key Benefits of Our UPS Systems
              </motion.h2>
              <motion.div
                className="w-24 h-1 bg-gradient-to-r from-blue-600 to-blue-700 mx-auto mb-6"
                initial={{ width: 0 }}
                whileInView={{ width: 96 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              ></motion.div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {upsBenefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  className="relative group"
                  whileHover={{ scale: 1.03 }}
                >
                  <div className={`relative flex items-start gap-4 p-6 rounded-xl shadow-xl ${benefit.gradient} ${benefit.shadowColor}`}>
                    <div className="shrink-0 p-3 rounded-xl bg-white/20 backdrop-blur">
                      {benefit.icon}
                    </div>
                    <div>
                      <h3 className="font-bold text-xl text-white mb-2">{benefit.title}</h3>
                      <p className="text-white text-opacity-95 font-medium leading-relaxed text-sm">{benefit.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Call-to-Action Section - Light Blue Color Scheme */}
        <div className="container mx-auto px-4 mb-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl shadow-md overflow-hidden border border-blue-300"
          >
            <div className="py-12 px-8 text-center relative">
              {/* Background decorative elements */}
              <div className="absolute top-0 right-0 w-40 h-40 bg-blue-300/20 rounded-full blur-xl"></div>
              <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-400/10 rounded-full blur-xl"></div>

              <h2 className="text-3xl font-bold mb-4 text-blue-700 relative z-10">
                Need More Information?
              </h2>

              <p className="text-blue-600 mb-8 max-w-3xl mx-auto text-lg relative z-10">
                Our team of experts is ready to help you with product specifications, custom solutions, pricing, and any other details you need about the KRYKARD UPS systems.
              </p>

              <Link to="/contact/Sales">
                <Button
                  className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg flex items-center justify-center gap-2 transition-all duration-300 mx-auto font-medium text-lg shadow-md border border-blue-400 relative z-10"
                >
                  <MailIcon className="h-5 w-5" />
                  <span>Get Quote</span>
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>


      </PageLayout>
    );
  };

  export default UPS;