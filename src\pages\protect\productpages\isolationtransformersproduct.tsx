import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Shield, Settings, Volume2, Award, CheckCircle, Star, Info, Mail, Download, Zap, ArrowRight, FileText } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";
import { motion } from 'framer-motion';

export default function TransformerProductPage() {
  const { type } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Map URL parameter to product title
  const getTransformerTitle = (urlType: string | undefined) => {
    switch(urlType) {
      case 'ultra-isolation-transformer':
        return 'Ultra Isolation Transformer';
      case 'galvanic-isolation-transformer':
        return 'Galvanic Isolation Transformer';
      case 'auto-transformer':
        return 'Auto Transformer';
      default:
        return 'Isolation Transformer';
    }
  };

  useEffect(() => {
    const init = async () => {
      try {
        // Start with loading state
        setIsLoading(true);

        // Validate the type parameter
        const validTypes = ['ultra-isolation-transformer', 'galvanic-isolation-transformer', 'auto-transformer'];
        if (!type || !validTypes.includes(type)) {
          setError('Invalid transformer type');
          navigate('/protect/isolation-transformers');
          return;
        }

        // Reset any previous errors
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };
    init();
  }, [type, navigate]);

  const transformerTitle = getTransformerTitle(type);

  // If loading, show loading state immediately
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center font-sans">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto mb-6"></div>
          <h2 className="text-2xl font-bold text-blue-600 mb-2">Loading...</h2>
          <p className="text-lg text-blue-500">Please wait while we load the transformer details</p>
        </div>
      </div>
    );
  }

  // If there's an error, show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center font-sans">
        <div className="text-center">
          <div className="text-red-600 mb-6">
            <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-red-600 mb-2">Error</h2>
          <p className="text-lg text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  const specifications = [
    { parameter: 'Ratings', '3Phase': '3 kVA to 1000 kVA', '1Phase': '1 kVA to 25 kVA' },
    { parameter: 'Reference Standard', '3Phase': 'IS 11171 : 1985 (Reaffirmed 2006)', '1Phase': '-' },
    { parameter: 'Type of Transformer', '3Phase': 'Floor mounted, natural air-cooled / oil-cooled depending on rating', '1Phase': 'Floor mounted, natural air-cooled' },
    { parameter: 'Configuration', '3Phase': 'Delta / star, LT or as per user specification', '1Phase': 'LT or as per user specification' },
    { parameter: 'Default Vector Group', '3Phase': 'Dyn11', '1Phase': '-' },
    { parameter: 'Type of Insulation', '3Phase': 'CRGO', '1Phase': '-' },
    { parameter: 'Winding', '3Phase': 'Copper Wire / Strip depending on rating wire / strip', '1Phase': '-' },
    { parameter: 'Load Regulation', '3Phase': 'Better than 3%', '1Phase': '-' },
    { parameter: 'Class of Insulation', '3Phase': 'H', '1Phase': '-' },
    { parameter: 'Efficiency', '3Phase': '≥ 97 kVA (>95%)', '1Phase': '-' },
    { parameter: 'Insulation Strength', '3Phase': 'AC rated input voltage and 50 Hz for 1 minute for all coils that are linear', '1Phase': '-' },
    { parameter: 'BIS Withstand', '3Phase': 'Withstands 2.5 kV for 1 minute (between windings & between windings and body)', '1Phase': '-' },
    { parameter: 'DC galvanic isolation', '3Phase': '> 1000 Mega Ohms - for LIP', '1Phase': '-' },
    { parameter: 'Common Mode Noise Rejection (for UIT only)', '3Phase': '> 100 Mega Ohms - for UIT', '1Phase': '-' },
    { parameter: 'Short Circuit Protection', '3Phase': 'Up to 10Hz 2 < 100 dB at 50 Hz for 60 dB', '1Phase': '-' },
    { parameter: 'Indications', '3Phase': 'HRC Fuse at 440 V system as a standard / MCB / MCCB protection is also an option', '1Phase': '-' },
    { parameter: 'Housing', '3Phase': 'LED Lamps For Output Presence Digital Voltmeter (DVM) - optional', '1Phase': 'Sheet metal housing provided with Input / Output terminations' }
  ];

  const features = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Wide Product Range",
      items: [
        "Step Up / Step Down Auto Transformers",
        "Galvanic Isolation Transformers",
        "Shielded Ultra Isolation Transformers",
        "K Rated Ultra Isolation Transformers",
        "Ratings 3 kVA to 1000 kVA 3 Phase and 1 kVA to 25 kVA 1 Phase / 2 Phase"
      ]
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: "Compliance to Standards, Efficiency & Reliability",
      items: [
        "Meets IS 11171 1985 Dry Type transformer standards",
        "Efficient designs using Class H Insulation",
        "Low impedance and temperature rise"
      ]
    },
    {
      icon: <Settings className="w-6 h-6" />,
      title: "Multiple add-on options",
      items: [
        "Multi-tap options available",
        "MCB / MCCB for Short Circuit protection",
        "RFI / EMI Filters",
        "TVSS / SPD for Transient protection",
        "Voltage / Current / Power Metering"
      ]
    },
    {
      icon: <Volume2 className="w-6 h-6" />,
      title: "Noise Attenuation",
      items: [
        "Good Transverse Mode noise attenuation",
        "Double Shielded for high Common Mode Noise Rejection (Ultra Isolation Transformers)"
      ]
    }
  ];
  return (
    <PageLayout
      title={transformerTitle}
      subtitle="High-quality power solutions for your needs"
      category="protect"
    >
      {/* Modern Blue Background Component */}
      <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100">
        {/* Abstract blue shapes */}
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-blue-200 rounded-bl-full opacity-30"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-blue-300 rounded-tr-full opacity-20"></div>

        {/* Floating geometric elements */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-400 rounded-full opacity-10 blur-xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-blue-500 rounded-full opacity-10 blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 font-sans -mx-6 -mt-24 pt-16 relative">
        {/* Enhanced Hero Section with Modern Design */}
        <section className="py-16 relative overflow-hidden">
          {/* Hero Background Elements - Blue Theme */}
          <div className="absolute inset-0 z-0">
            <div className="absolute top-0 right-0 w-1/2 md:w-3/4 h-full bg-blue-50 rounded-bl-[50px] md:rounded-bl-[100px] transform -skew-x-6 md:-skew-x-12"></div>
            <div className="absolute bottom-20 left-0 w-32 h-32 md:w-64 md:h-64 bg-blue-400 rounded-full opacity-10"></div>
            <div className="absolute top-1/3 right-1/4 w-24 h-24 md:w-48 md:h-48 bg-blue-300 rounded-full opacity-15"></div>
          </div>

          <div className="relative z-10 px-4 max-w-7xl mx-auto">
            {/* Main Title Section */}
            <motion.div
              className="text-center text-blue-800 p-4 sm:p-6 md:p-8 overflow-hidden relative mb-8 sm:mb-12 md:mb-16"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
            >
              <div className="relative z-10">
                <motion.h1
                  className="text-2xl sm:text-3xl md:text-5xl font-extrabold tracking-tight mb-2 sm:mb-3 md:mb-4 text-blue-800"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.2 }}
                >
                  KRYKARD <span className="text-blue-600">{transformerTitle}</span>
                </motion.h1>

                <motion.p
                  className="text-base sm:text-lg md:text-2xl font-medium mb-4 sm:mb-6 md:mb-8 text-blue-600"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.3 }}
                >
                  Advanced Power Solutions for Critical Applications
                </motion.p>

                <motion.div
                  className="bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold py-2 sm:py-3 md:py-4 px-4 sm:px-6 md:px-8 rounded-lg inline-block shadow-lg transform hover:scale-105 transition-transform duration-300 text-xs sm:text-sm md:text-base"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.4 }}
                >
                  PREMIUM QUALITY • SAFETY • RELIABILITY • EFFICIENCY
                </motion.div>
              </div>
            </motion.div>

            {/* Hero Content Area - Left content, Right image */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center mb-8 sm:mb-12 md:mb-16">
              {/* Left side: Content */}
              <motion.div
                className="space-y-4 sm:space-y-6 md:space-y-8 order-2 lg:order-1"
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
              >
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-blue-900 mb-2 sm:mb-3 md:mb-4">Enterprise-Grade Power Protection</h2>
                  <div className="h-1 sm:h-1.5 w-16 sm:w-20 md:w-24 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mb-3 sm:mb-4 md:mb-6"></div>
                  <p className="text-sm sm:text-base md:text-lg text-blue-800 leading-relaxed">
                    Premium quality transformers designed for safety, reliability, and efficiency across various industrial applications. Our advanced power solutions provide superior power quality and protection for critical systems.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-gradient-to-r from-blue-50/50 to-blue-100/70 p-3 sm:p-4 md:p-6 rounded-xl border border-blue-200/30 backdrop-blur-sm"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <h3 className="text-base sm:text-lg md:text-xl font-bold mb-2 sm:mb-3 md:mb-4 text-blue-800">Perfect for:</h3>
                  <ul className="space-y-2 sm:space-y-3 md:space-y-4">
                    {[
                      {icon: "🏭", text: "Industrial Control Systems"},
                      {icon: "🏥", text: "Medical Equipment & Healthcare"},
                      {icon: "🏢", text: "Commercial Buildings"},
                      {icon: "💻", text: "IT Infrastructure & Data Centers"},
                      {icon: "🔌", text: "Sensitive Electronic Equipment"}
                    ].map((item, index) => (
                      <motion.li
                        key={index}
                        className="flex items-center group"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                      >
                        <motion.span
                          className="text-lg sm:text-xl md:text-2xl mr-2 sm:mr-3 md:mr-4 transform group-hover:scale-110 transition-transform"
                          animate={{ rotate: [0, 8, 0] }}
                          transition={{ duration: 6, repeat: Infinity, repeatType: "reverse" }}
                        >
                          {item.icon}
                        </motion.span>
                        <span className="text-blue-700 font-medium group-hover:text-blue-600 transition-colors text-xs sm:text-sm md:text-base">
                          {item.text}
                        </span>
                      </motion.li>
                    ))}
                  </ul>
                </motion.div>

                <motion.div
                  className="flex flex-wrap gap-3 sm:gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <motion.button
                    onClick={() => {
                      window.location.href = '/contact/sales';
                    }}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 md:py-3 rounded-lg shadow-lg flex items-center gap-1 sm:gap-2 transition-all duration-300 text-xs sm:text-sm md:text-base"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <span>Request Quote</span>
                    <ArrowRight size={14} className="sm:hidden" />
                    <ArrowRight size={16} className="hidden sm:block md:hidden" />
                    <ArrowRight size={18} className="hidden md:block" />
                  </motion.button>

                  <motion.button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = '/brochures/isolation-transformers-brochure.pdf';
                      link.download = 'Isolation-Transformers-Brochure.pdf';
                      link.click();
                    }}
                    className="border-2 border-blue-600 text-blue-700 hover:bg-blue-50 px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 md:py-3 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-1 sm:gap-2 text-xs sm:text-sm md:text-base"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FileText size={14} className="sm:hidden" />
                    <FileText size={16} className="hidden sm:block md:hidden" />
                    <FileText size={18} className="hidden md:block" />
                    <span>View Brochure</span>
                  </motion.button>
                </motion.div>
              </motion.div>

              {/* Right side: Transformer Image with Blue Background Design */}
              <motion.div
                className="relative flex justify-center order-1 lg:order-2"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8 }}
              >
                {/* Blue gradient background similar to clampmeters */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-200 to-blue-50 rounded-full opacity-20 blur-xl transform scale-90"></div>

                <motion.div
                  className="w-full max-w-3xl h-[300px] sm:h-[500px] md:h-[700px] flex items-center justify-center relative z-10"
                  animate={{
                    y: [0, -20, 0],
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                  }}
                >
                  {/* Additional blue decorative elements */}
                  <div className="absolute top-10 left-10 w-16 h-16 bg-blue-300 rounded-full opacity-20 blur-lg"></div>
                  <div className="absolute bottom-10 right-10 w-24 h-24 bg-blue-400 rounded-full opacity-15 blur-lg"></div>

                  {/* Transformer Image */}
                  <img
                    src="/transformers/isolation-transformer-main.png"
                    alt={transformerTitle}
                    className="max-w-full max-h-full object-contain drop-shadow-2xl transform hover:scale-115 transition-transform duration-700 relative z-10"
                    style={{ filter: "drop-shadow(0 20px 30px rgba(59, 130, 246, 0.3))" }}
                    onError={(e) => {
                      // Fallback to a generic transformer image or placeholder
                      e.currentTarget.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23f3f4f6'/%3E%3Ctext x='200' y='150' text-anchor='middle' dy='.3em' fill='%236b7280' font-family='Arial, sans-serif' font-size='18'%3ETransformer Image%3C/text%3E%3C/svg%3E";
                    }}
                  />
                </motion.div>
              </motion.div>
            </div>

            {/* Quality Indicators */}
            <motion.div
              className="flex items-center justify-center gap-6 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 md:w-7 md:h-7 text-yellow-400 fill-current" />
                ))}
                <span className="ml-2 md:ml-4 text-sm md:text-xl text-blue-700 font-medium">Industry Leading Quality</span>
              </div>
            </motion.div>
          </div>
        </section>

      {/* Overview Section - Full Width */}
      <div className="w-full bg-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Advanced Power Solutions
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Our Isolation & Auto Transformers provide superior power quality, safety, and reliability for critical applications across industries. Built to meet international standards with cutting-edge technology, these transformers ensure optimal performance and protection for your valuable equipment and systems.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-blue-200">
                <div className="flex flex-col items-center text-center mb-6">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-4 shadow-lg mb-4">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
                </div>
                <ul className="space-y-4">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-1 mr-3 flex-shrink-0" />
                      <span className="text-base text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technical Specifications Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-900 to-blue-800">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technical Specifications
            </h2>
            <p className="text-xl text-blue-100 leading-relaxed text-justify max-w-4xl mx-auto">
              Detailed technical parameters and standards ensuring optimal performance and reliability across all applications.
            </p>
          </div>

          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-700 to-blue-800">
                    <th className="px-8 py-6 text-left text-lg font-bold text-white border-b-2 border-blue-600">
                      Parameter
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      3-Phase
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      1-Phase / 2-Phase
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {specifications.map((spec, index) => (
                    <tr key={index} className={`${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'} hover:bg-blue-100 transition-colors duration-200`}>
                      <td className="px-8 py-5 text-base font-bold text-gray-900 border-b border-blue-200 bg-gradient-to-r from-blue-100 to-blue-50">
                        {spec.parameter}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['3Phase']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['1Phase']}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-blue-50 px-8 py-6 border-t-2 border-blue-200">
              <p className="text-base text-gray-800 font-medium flex items-center justify-center">
                <Info className="w-6 h-6 inline mr-3 text-blue-600" />
                All specifications are subject to standard tolerances and testing conditions as per IS 11171:1985 standards.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Key Features Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-100 to-blue-200">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Key Features & Benefits
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Discover the advanced features that make our transformers the preferred choice for critical power applications. Each feature is designed to deliver maximum performance, safety, and reliability.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-3xl shadow-2xl p-10 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 border-2 border-blue-300">
                <div className="flex items-center mb-8">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-5 shadow-xl">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 ml-6">{feature.title}</h3>
                </div>
                <div className="space-y-5">
                  {feature.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-start">
                      <div className="bg-green-100 rounded-full p-2 mr-4 mt-1">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                      <span className="text-lg text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action Section - Full Width */}
      <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-20">
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              Ready to Power Your Operations?
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed text-justify">
              Get in touch with our experts to find the perfect transformer solution for your needs. We provide comprehensive support from consultation to installation and maintenance.
            </p>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // Redirect to Sales page
                  window.location.href = '/contact/sales';
                }}
                className="bg-blue-600 text-white px-10 py-5 rounded-xl font-bold text-xl hover:bg-blue-500 transition-all duration-300 transform hover:scale-105 shadow-xl"
              >
                <Mail className="w-6 h-6 inline mr-3" />
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Add CSS for blue grid pattern and effects */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .bg-grid-pattern {
            background-image:
              linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
          }

          .text-shadow-blue {
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
          }

          .shadow-blue {
            box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
          }

          .watermark {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Ctext x='50%' y='50%' dominant-baseline='middle' text-anchor='middle' font-family='Arial' font-weight='bold' font-size='24' fill='rgba(59, 130, 246, 0.08)' transform='rotate(-45, 100, 100)'%3EKRYKARD%3C/text%3E%3C/svg%3E");
            background-repeat: repeat;
            background-size: 200px 200px;
            pointer-events: none;
            z-index: -1;
          }
        `
      }} />
    </PageLayout>
  );
}